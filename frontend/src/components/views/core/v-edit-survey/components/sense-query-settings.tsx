import { Component, Prop, State, h, Listen } from '@stencil/core';
import {
  UpdateByAttributeApi,
  GenerateUpdateByAttributePayload,
  ConstructApiUrl,
} from '../../../../../global/script/helpers';
import { Var } from '../../../../../global/script/var';

interface Category {
  value: string;
  label: string;
}

@Component({
  tag: 'sense-query-settings',
  styleUrl: '../v-edit-survey.css',
  shadow: true,
})
export class SenseQuerySettings {
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() updatingField: string = '';

  // Question field status
  @State() questionMessage: string = '';
  @State() questionSuccess: boolean = false;

  // Categories field status
  @State() categoriesMessage: string = '';
  @State() categoriesSuccess: boolean = false;

  // Thank You Message field status
  @State() thankYouMessageMessage: string = '';
  @State() thankYouMessageSuccess: boolean = false;

  @State() newCategory: string = '';
  @State() question: string = '';
  @State() thankYouMessage: string = '';
  @State() isEditingQuestion: boolean = false;
  @State() isEditingThankYou: boolean = false;

  componentWillLoad() {
    this.question = this.survey.config?.question;
    this.thankYouMessage = this.survey.config?.thankYouMessage;
  }

  @Listen('inputEvent')
  handleInputEvent(event: CustomEvent) {
    if (event.detail.name === 'newCategory') {
      this.newCategory = event.detail.value;
      console.log('New category input:', this.newCategory);
    }
  }

  @Listen('listWithDeleteEvent')
  async handleListWithDeleteEvent(event: CustomEvent) {
    if (event.detail.name === 'deleteCategory') {
      await this.removeCategory(event.detail.value);
    }
  }

  @Listen('buttonClickEvent')
  async handleButtonClickEvent(event: CustomEvent) {
    if (event.detail.action === 'addCategory') {
      await this.addCategory();
    }
  }

  @Listen('editableTextEvent')
  async handleEditableTextEvent(event: CustomEvent) {
    if (event.detail.attribute === 'question') {
      await this.updateQuestion(event.detail.value);
    } else if (event.detail.attribute === 'thankYouMessage') {
      await this.updateThankYouMessage(event.detail.value);
    }
  }

  private async updateQuestion(newQuestion: string) {
    this.updatingField = 'question';
    if (!newQuestion || newQuestion.trim() === '') {
      this.questionMessage = 'Question cannot be empty';
      this.questionSuccess = false;
      this.updatingField = '';
      return;
    }
    const updatedConfig = { ...this.survey.config, question: newQuestion };
    await this.updateConfig(updatedConfig, { attribute: 'question', value: newQuestion });
  }

  private async addCategory() {
    this.updatingField = 'categories';

    if (!this.newCategory || this.newCategory.trim() === '') {
      this.categoriesMessage = 'Category cannot be empty';
      this.categoriesSuccess = false;
      this.updatingField = '';
      return;
    }

    const currentCategories = [...(this.survey.config.categories || [])] as Category[];

    let isDuplicate = false;
    for (let i = 0; i < currentCategories.length; i++) {
      if (
        currentCategories[i].value === this.newCategory ||
        currentCategories[i].label === this.newCategory
      ) {
        isDuplicate = true;
        break;
      }
    }

    if (isDuplicate) {
      this.categoriesMessage = 'Category already exists';
      this.categoriesSuccess = false;
      this.updatingField = '';
      return;
    }

    const newCategoryObj: Category = {
      label: this.newCategory,
      value: this.newCategory.toLowerCase().replace(/\s+/g, '-'),
    };

    const updatedCategories: Category[] = [...currentCategories, newCategoryObj];
    const updatedConfig = { ...this.survey.config, categories: updatedCategories };

    const success = await this.updateConfig(updatedConfig, {
      attribute: 'categories',
      value: updatedCategories,
    });
    if (success) {
      this.newCategory = '';
    }
  }

  private async removeCategory(categoryToRemove: string) {
    this.updatingField = 'categories';

    const currentCategories = [...(this.survey.config.categories || [])] as Category[];

    let updatedCategories: Category[] = [];
    for (let i = 0; i < currentCategories.length; i++) {
      if (currentCategories[i].value !== categoryToRemove) {
        updatedCategories.push(currentCategories[i]);
      }
    }

    const updatedConfig = { ...this.survey.config, categories: updatedCategories };
    await this.updateConfig(updatedConfig, { attribute: 'categories', value: updatedCategories });
  }

  private async updateThankYouMessage(newMessage: string) {
    this.updatingField = 'thankYouMessage';
    const updatedConfig = { ...this.survey.config, thankYouMessage: newMessage };
    await this.updateConfig(updatedConfig, { attribute: 'thankYouMessage', value: newMessage });
  }

  private async updateConfig(updatedConfig: any, obj: any) {
    // Set field-specific messages based on the attribute being updated
    if (obj.attribute === 'question') {
      this.isEditingQuestion = true;
    } else if (obj.attribute === 'thankYouMessage') {
      this.isEditingThankYou = true;
    }

    const updatePayload = GenerateUpdateByAttributePayload('config', updatedConfig);

    try {
      const { success, message } = await UpdateByAttributeApi(
        `${Var.api.endpoint.surveys}/${this.surveyId}`,
        updatePayload,
      );

      // Update field-specific messages
      if (obj.attribute === 'question') {
        this.questionMessage = message;
        this.questionSuccess = success;
      } else if (obj.attribute === 'categories') {
        this.categoriesMessage = message;
        this.categoriesSuccess = success;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessageMessage = message;
        this.thankYouMessageSuccess = success;
      }

      if (success) {
        this.survey.config = updatedConfig;
        return true;
      }
      return false;
    } catch (error) {
      // Set field-specific error messages
      const errorMessage = `An error occurred while updating ${obj.attribute}`;

      if (obj.attribute === 'question') {
        this.questionMessage = errorMessage;
        this.questionSuccess = false;
      } else if (obj.attribute === 'categories') {
        this.categoriesMessage = errorMessage;
        this.categoriesSuccess = false;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessageMessage = errorMessage;
        this.thankYouMessageSuccess = false;
      }

      console.error(error);
      return false;
    } finally {
      if (obj.attribute === 'question') {
        this.question = obj.value;
        this.isEditingQuestion = false;
      } else if (obj.attribute === 'thankYouMessage') {
        this.thankYouMessage = obj.value;
        this.isEditingThankYou = false;
      }
      this.updatingField = '';
    }
  }

  private formatCategoriesForList(): string {
    if (!this.survey?.config?.categories?.length) return '[]';

    const categories = this.survey.config.categories as Category[];
    return JSON.stringify(categories);
  }

  render() {
    return (
      <div class="settings-section">
        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>1. What question would you like to ask?</strong>
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Question"
          type="text"
          value={this.question}
          entity="survey"
          attribute="question"
          bypass={true}
          active={this.isEditingQuestion}
        ></e-text-editable>

        {this.questionMessage && this.updatingField !== 'question' && (
          <p-notification
            message={this.questionMessage}
            theme={this.questionSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>2. What categories would you like to organize responses into? (Optional)</strong>
        </e-text>
        <l-spacer value={1}></l-spacer>
        <l-row justifyContent="flex-start">
          <e-input
            type="text"
            name="newCategory"
            placeholder="e.g. Product, Pricing, Installation"
            value={this.newCategory}
          ></e-input>
          <l-spacer variant="horizontal" value={0.5}></l-spacer>
          <e-button
            variant="ghost"
            disabled={!this.newCategory}
            onClick={() => this.addCategory()}
            action="addCategory"
          >
            Add
          </e-button>
        </l-row>
        <l-spacer value={1}></l-spacer>
        <p-list-with-delete
          name="deleteCategory"
          items={this.formatCategoriesForList()}
          emptyMessage="No categories added yet"
        ></p-list-with-delete>

        {this.updatingField === 'categories' && (
          <div class="loading-indicator">
            <e-spinner theme="dark"></e-spinner>
            <l-spacer variant="horizontal" value={0.5}></l-spacer>
            <e-text variant="footnote">Updating...</e-text>
          </div>
        )}

        {this.categoriesMessage && this.updatingField !== 'categories' && (
          <p-notification
            message={this.categoriesMessage}
            theme={this.categoriesSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}

        <l-spacer value={3}></l-spacer>
        <e-text>
          <strong>3. Thank You Message</strong>
        </e-text>
        <e-text variant="footnote">
          This message will be shown to respondents after they submit their response
        </e-text>
        <l-spacer value={0.5}></l-spacer>
        <e-text-editable
          label="Thank You Message"
          type="text"
          value={this.thankYouMessage}
          entity="survey"
          attribute="thankYouMessage"
          bypass={true}
          active={this.isEditingThankYou}
        ></e-text-editable>
        {this.thankYouMessageMessage && this.updatingField !== 'thankYouMessage' && (
          <p-notification
            message={this.thankYouMessageMessage}
            theme={this.thankYouMessageSuccess ? 'success' : 'danger'}
          ></p-notification>
        )}
      </div>
    );
  }
}
