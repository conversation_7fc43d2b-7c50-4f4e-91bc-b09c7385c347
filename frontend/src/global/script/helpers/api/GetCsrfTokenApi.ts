import { Var } from '../../var';
import { Store } from '../../store';
import { ApiWrapper } from './ApiWrapper';

export const GetCsrfTokenApi = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const result = await ApiWrapper(Var.api.endpoint.csrf, {
      method: 'GET',
      includeCsrf: false, // Don't include CSRF token when fetching it
    });

    // Fallback: check response payload for token (backward compatibility)
    if (result.success && result.payload?.token && !Store.csrfToken) {
      Store.csrfToken = result.payload.token;
      console.debug(
        'CSRF token fetched from response body:',
        result.payload.token.substring(0, 10) + '...',
      );
    }

    // Check if we have a token (from either ApiWrapper headers or response payload)
    if (Store.csrfToken) {
      return {
        success: true,
        message: 'CSRF token fetched successfully',
      };
    }

    // No token found in either location
    const errorMessage = result.message || 'No CSRF token found in response headers or body';
    console.error('Failed to fetch CSRF token:', errorMessage);
    return {
      success: false,
      message: errorMessage,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error fetching CSRF token:', errorMessage);
    return {
      success: false,
      message: 'Failed to fetch CSRF token',
    };
  }
};
