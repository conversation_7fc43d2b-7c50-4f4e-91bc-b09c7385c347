import { Store } from '../../store';
import { ConstructApiUrl } from './ConstructApiUrl';
import { CsrfValidator } from '../security/CsrfValidator';
import { RateLimitHandler } from './RateLimitHandler';

function extractRateLimitInfo(response: Response) {
  const limit = response.headers.get('RateLimit-Limit');
  const remaining = response.headers.get('RateLimit-Remaining');
  const reset = response.headers.get('RateLimit-Reset');
  const retryAfter = response.headers.get('Retry-After');

  return {
    limit: limit ? parseInt(limit, 10) : undefined,
    remaining: remaining ? parseInt(remaining, 10) : undefined,
    resetTime: reset ? parseInt(reset, 10) : undefined,
    retryAfter: retryAfter ? parseInt(retryAfter, 10) : undefined,
  };
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload?: any;
  rateLimitInfo?: {
    isRateLimited: boolean;
    retryAfter?: number;
    limit?: number;
    remaining?: number;
    resetTime?: number;
  };
}

interface ApiOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
  includeCsrf?: boolean;
  retryOnRateLimit?: boolean;
  retryOnCsrfError?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export const ApiWrapper = async (
  endpoint: string,
  options: ApiOptions = { method: 'GET' },
): Promise<ApiResponse> => {
  const maxRetries = options.maxRetries ?? 2;
  const retryDelay = options.retryDelay ?? 1000;
  const shouldRetryOnRateLimit = options.retryOnRateLimit ?? false;
  const shouldRetryOnCsrfError = options.retryOnCsrfError ?? true; // Default to true for CSRF retry

  // Check if endpoint is already rate limited
  if (RateLimitHandler.isEndpointRateLimited(endpoint)) {
    const timeRemaining = RateLimitHandler.getTimeUntilReset(endpoint);
    const message = RateLimitHandler.getRateLimitMessage(endpoint);

    console.warn(`Endpoint ${endpoint} is rate limited for ${timeRemaining} more seconds`);

    return {
      success: false,
      message,
      payload: null,
      rateLimitInfo: {
        isRateLimited: true,
        retryAfter: timeRemaining,
      },
    };
  }

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    const result = await performApiRequest(endpoint, options);

    // Record rate limit information
    if (result.rateLimitInfo) {
      if (result.rateLimitInfo.isRateLimited) {
        RateLimitHandler.recordRateLimit(endpoint, result.rateLimitInfo);
      } else if (RateLimitHandler.shouldWarnAboutRateLimit(result.rateLimitInfo)) {
        console.warn(
          'Approaching rate limit:',
          RateLimitHandler.getRateLimitWarning(result.rateLimitInfo),
        );
      }
    }

    // If rate limited and retries are enabled, wait and retry
    if (result.rateLimitInfo?.isRateLimited && shouldRetryOnRateLimit && attempt < maxRetries) {
      const waitTime = result.rateLimitInfo.retryAfter
        ? result.rateLimitInfo.retryAfter * 1000
        : RateLimitHandler.calculateRetryDelay(attempt, retryDelay);

      console.warn(
        `Rate limited, retrying in ${waitTime}ms (attempt ${attempt + 1}/${maxRetries + 1})`,
      );
      await sleep(waitTime);
      continue;
    }

    // If CSRF error and retries are enabled, refresh token and retry
    if (
      !result.success &&
      result.message?.includes('Invalid security token') &&
      shouldRetryOnCsrfError &&
      attempt < maxRetries &&
      !endpoint.includes('/auth/logout') // Don't retry logout CSRF errors
    ) {
      console.warn(
        `CSRF error, refreshing token and retrying (attempt ${attempt + 1}/${maxRetries + 1})`,
      );

      try {
        // Import GetCsrfTokenApi dynamically to avoid circular dependency
        const { GetCsrfTokenApi } = await import('./GetCsrfTokenApi');
        const refreshResult = await GetCsrfTokenApi();

        if (refreshResult.success) {
          console.debug('CSRF token refreshed, retrying request');
          await sleep(500); // Small delay before retry
          continue;
        } else {
          console.warn('Failed to refresh CSRF token, not retrying');
        }
      } catch (refreshError) {
        console.warn('Error refreshing CSRF token for retry:', refreshError);
      }
    }

    return result;
  }

  // This should never be reached, but TypeScript requires it
  throw new Error('Unexpected error in ApiWrapper retry logic');
};

async function performApiRequest(endpoint: string, options: ApiOptions): Promise<ApiResponse> {
  const url = ConstructApiUrl(endpoint);

  // Default headers
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add CSRF token for state-changing requests or when explicitly requested
  if (options.includeCsrf !== false && (options.method !== 'GET' || options.includeCsrf === true)) {
    if (!CsrfValidator.isTokenValid()) {
      console.warn(
        'Warning: CSRF token is invalid when creating request headers',
        CsrfValidator.getTokenInfo(),
      );
    }
    defaultHeaders['X-CSRF-Token'] = Store.csrfToken || '';
  }

  // Merge headers
  const headers = { ...defaultHeaders, ...options.headers };

  // Prepare fetch options
  const fetchOptions: RequestInit = {
    method: options.method,
    headers,
    credentials: options.credentials || 'include',
  };

  // Add body for non-GET requests
  if (options.body && options.method !== 'GET') {
    fetchOptions.body =
      typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url, fetchOptions);

    // Always check for updated CSRF token in response headers
    const csrfToken = response.headers.get('X-CSRF-Token');
    if (csrfToken) {
      Store.csrfToken = csrfToken;
      console.debug('CSRF token updated from response header:', csrfToken.substring(0, 10) + '...');
    }

    // Extract rate limiting information from headers
    const rateLimitInfo = extractRateLimitInfo(response);

    // Handle non-JSON responses
    let data: any;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = {
        success: response.ok,
        message: response.ok ? 'Request successful' : 'Request failed',
        payload: null,
      };
    }

    // Handle HTTP error status codes
    if (!response.ok) {
      console.error('API request failed:', {
        url,
        status: response.status,
        statusText: response.statusText,
        data,
      });

      // Handle rate limiting (429 status)
      if (response.status === 429) {
        console.warn('Rate limit exceeded:', {
          url,
          rateLimitInfo,
          message: data?.message,
        });

        // Check for CSRF token in rate limit response
        if (data?.csrfToken) {
          Store.csrfToken = data.csrfToken;
          console.debug('CSRF token updated from rate limit response');
        }

        return {
          success: false,
          message: data?.message || 'Rate limit exceeded. Please try again later.',
          payload: data?.payload || null,
          rateLimitInfo: {
            isRateLimited: true,
            ...rateLimitInfo,
          },
        };
      }

      // Handle CSRF token errors specifically
      if (response.status === 403 && data?.code === 'CSRF_TOKEN_INVALID') {
        console.warn('CSRF token invalid, clearing stored token and attempting refresh');
        CsrfValidator.clearToken();

        // Special handling for logout endpoint - CSRF errors during logout are expected
        if (endpoint.includes('/auth/logout')) {
          console.debug('CSRF error during logout is expected due to session destruction');
        } else {
          // For other endpoints, try to refresh the CSRF token automatically
          console.debug('Attempting to refresh CSRF token after validation failure');
          try {
            // Import GetCsrfTokenApi dynamically to avoid circular dependency
            const { GetCsrfTokenApi } = await import('./GetCsrfTokenApi');
            const refreshResult = await GetCsrfTokenApi();
            if (refreshResult.success) {
              console.debug('CSRF token refreshed successfully after validation failure');
            } else {
              console.warn(
                'Failed to refresh CSRF token after validation failure:',
                refreshResult.message,
              );
            }
          } catch (refreshError) {
            console.warn('Error refreshing CSRF token after validation failure:', refreshError);
          }
        }
      }

      return {
        success: false,
        message: data?.message || `HTTP ${response.status}: ${response.statusText}`,
        payload: data?.payload || null,
        rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
      };
    }

    return {
      success: data?.success !== false,
      message: data?.message || 'Request successful',
      payload: data?.payload || data,
      rateLimitInfo: rateLimitInfo.limit ? { isRateLimited: false, ...rateLimitInfo } : undefined,
    };
  } catch (error) {
    console.error('API request error:', {
      url,
      error: error instanceof Error ? error.message : String(error),
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Network error occurred',
      payload: null,
    };
  }
}
