import { ApiWrapper } from './ApiWrapper';

export const UpdateByAttributeApi = async (endpoint: string, payload: any) => {
  try {
    const result = await <PERSON><PERSON>Wrapper(endpoint, {
      method: 'PUT',
      body: payload,
    });

    return {
      success: result.success,
      message: result.message,
      payload: result.payload,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error in UpdateByAttributeApi:', errorMessage);
    return {
      success: false,
      message: 'Failed to update data',
      payload: null,
    };
  }
};
