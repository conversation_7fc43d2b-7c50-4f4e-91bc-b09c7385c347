import { Store } from '../../store';

export class CsrfValidator {
  static isTokenValid(): boolean {
    return !!Store.csrfToken && Store.csrfToken.length > 0;
  }

  static async ensureValidToken(
    fetchTokenFn?: () => Promise<{ success: boolean; message: string }>,
  ): Promise<boolean> {
    if (this.isTokenValid()) {
      return true;
    }

    if (!fetchTokenFn) {
      console.warn('CSRF token missing and no fetch function provided');
      return false;
    }

    console.debug('CSRF token missing or invalid, fetching new token...');
    const result = await fetchTokenFn();

    if (result.success) {
      console.debug('CSRF token refreshed successfully');
      return true;
    } else {
      console.error('Failed to refresh CSRF token:', result.message);
      return false;
    }
  }

  static clearToken(): void {
    Store.csrfToken = '';
  }

  static getTokenInfo(): { hasToken: boolean; tokenPrefix: string; tokenLength: number } {
    const hasToken = this.isTokenValid();
    return {
      hasToken,
      tokenPrefix: hasToken ? Store.csrfToken.substring(0, 10) + '...' : 'none',
      tokenLength: Store.csrfToken.length,
    };
  }

  static isTokenFormatValid(): boolean {
    if (!Store.csrfToken) return false;

    const token = Store.csrfToken;
    const isValidLength = token.length >= 32 && token.length <= 256;
    const isValidFormat = /^[a-zA-Z0-9+/=_-]+$/.test(token);

    return isValidLength && isValidFormat;
  }
}
